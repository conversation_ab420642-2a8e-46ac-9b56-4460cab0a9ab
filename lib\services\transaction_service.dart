import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/transaction_model.dart';
import '../firebase/firebase_service.dart';

class TransactionService {
  static final TransactionService _instance = TransactionService._internal();
  factory TransactionService() => _instance;
  TransactionService._internal();

  static const String _transactionsKey = 'user_transactions';
  static const String _balanceKey = 'account_balance';
  
  final FirebaseService _firebaseService = FirebaseService();
  List<Transaction> _transactions = [];
  double _currentBalance = 125400.00; // Default balance

  // Initialize with sample data for demo
  Future<void> initialize() async {
    await _loadTransactions();
    if (_transactions.isEmpty) {
      await _createSampleTransactions();
    }
  }

  // Get all transactions
  List<Transaction> getAllTransactions() {
    return List.from(_transactions)..sort((a, b) => b.timestamp.compareTo(a.timestamp));
  }

  // Get recent transactions (last 5)
  List<Transaction> getRecentTransactions({int limit = 5}) {
    final sorted = getAllTransactions();
    return sorted.take(limit).toList();
  }

  // Get transactions for a specific period
  List<Transaction> getTransactionsForPeriod(DateTime start, DateTime end) {
    return _transactions.where((transaction) {
      return transaction.timestamp.isAfter(start) && 
             transaction.timestamp.isBefore(end);
    }).toList()..sort((a, b) => b.timestamp.compareTo(a.timestamp));
  }

  // Get transaction summary for period
  TransactionSummary getTransactionSummary(DateTime start, DateTime end) {
    final periodTransactions = getTransactionsForPeriod(start, end);
    
    double totalIncome = 0;
    double totalExpense = 0;
    
    for (final transaction in periodTransactions) {
      if (transaction.isCredit) {
        totalIncome += transaction.amount;
      } else {
        totalExpense += transaction.amount;
      }
    }
    
    return TransactionSummary(
      totalIncome: totalIncome,
      totalExpense: totalExpense,
      netAmount: totalIncome - totalExpense,
      transactionCount: periodTransactions.length,
      periodStart: start,
      periodEnd: end,
    );
  }

  // Get current balance
  double getCurrentBalance() => _currentBalance;

  // Add new transaction
  Future<void> addTransaction(Transaction transaction) async {
    _transactions.add(transaction);
    
    // Update balance
    if (transaction.isCredit) {
      _currentBalance += transaction.amount;
    } else {
      _currentBalance -= transaction.amount;
    }
    
    await _saveTransactions();
    await _saveBalance();
    
    // Upload to Firebase
    try {
      await _firebaseService.uploadTransactionData(
        'current_user', // In real app, get from auth service
        transaction.toJson(),
      );
    } catch (e) {
      print('Failed to upload transaction to Firebase: $e');
    }
  }

  // Load transactions from local storage
  Future<void> _loadTransactions() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final transactionsJson = prefs.getString(_transactionsKey);
      final balance = prefs.getDouble(_balanceKey);
      
      if (balance != null) {
        _currentBalance = balance;
      }
      
      if (transactionsJson != null) {
        final List<dynamic> transactionsList = jsonDecode(transactionsJson);
        _transactions = transactionsList
            .map((json) => Transaction.fromJson(json))
            .toList();
      }
    } catch (e) {
      print('Failed to load transactions: $e');
    }
  }

  // Save transactions to local storage
  Future<void> _saveTransactions() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final transactionsJson = jsonEncode(
        _transactions.map((t) => t.toJson()).toList(),
      );
      await prefs.setString(_transactionsKey, transactionsJson);
    } catch (e) {
      print('Failed to save transactions: $e');
    }
  }

  // Save balance to local storage
  Future<void> _saveBalance() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setDouble(_balanceKey, _currentBalance);
    } catch (e) {
      print('Failed to save balance: $e');
    }
  }

  // Create sample transactions for demo
  Future<void> _createSampleTransactions() async {
    final now = DateTime.now();
    
    final sampleTransactions = [
      Transaction(
        id: 'txn_001',
        title: 'Salary Credit',
        description: 'TCS Limited - Monthly Salary',
        amount: 85000.00,
        type: TransactionType.credit,
        category: TransactionCategory.salary,
        timestamp: now.subtract(const Duration(days: 1)),
        icon: Transaction._getIconForCategory(TransactionCategory.salary),
        recipientName: 'TCS Limited',
        referenceNumber: 'SAL${now.millisecondsSinceEpoch}',
      ),
      Transaction(
        id: 'txn_002',
        title: 'UPI Transfer',
        description: 'To Priya Sharma - Rent Payment',
        amount: 25000.00,
        type: TransactionType.debit,
        category: TransactionCategory.transfer,
        timestamp: now.subtract(const Duration(days: 2)),
        icon: Transaction._getIconForCategory(TransactionCategory.transfer),
        recipientName: 'Priya Sharma',
        recipientAccount: '****1234',
        referenceNumber: 'UPI${now.millisecondsSinceEpoch}',
      ),
      Transaction(
        id: 'txn_003',
        title: 'Online Purchase',
        description: 'Amazon India - Electronics',
        amount: 12499.00,
        type: TransactionType.debit,
        category: TransactionCategory.shopping,
        timestamp: now.subtract(const Duration(days: 3)),
        icon: Transaction._getIconForCategory(TransactionCategory.shopping),
        recipientName: 'Amazon India',
        referenceNumber: 'AMZ${now.millisecondsSinceEpoch}',
      ),
      Transaction(
        id: 'txn_004',
        title: 'Interest Credit',
        description: 'Savings Account Interest',
        amount: 2850.00,
        type: TransactionType.credit,
        category: TransactionCategory.interest,
        timestamp: now.subtract(const Duration(days: 5)),
        icon: Transaction._getIconForCategory(TransactionCategory.interest),
        referenceNumber: 'INT${now.millisecondsSinceEpoch}',
      ),
      Transaction(
        id: 'txn_005',
        title: 'Electricity Bill',
        description: 'MSEB - Monthly Bill Payment',
        amount: 3200.00,
        type: TransactionType.debit,
        category: TransactionCategory.utility,
        timestamp: now.subtract(const Duration(days: 7)),
        icon: Transaction._getIconForCategory(TransactionCategory.utility),
        recipientName: 'MSEB',
        referenceNumber: 'BILL${now.millisecondsSinceEpoch}',
      ),
    ];
    
    _transactions.addAll(sampleTransactions);
    await _saveTransactions();
  }
}
