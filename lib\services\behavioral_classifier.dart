import 'dart:math';
import 'package:flutter/material.dart';
import '../models/behavioral_data_model.dart';

/// 🚀 HACKATHON FLAGSHIP: Simple ML-style Behavioral Classifier
/// Real-time behavioral pattern analysis for passwordless authentication
class BehavioralClassifier {
  static final BehavioralClassifier _instance = BehavioralClassifier._internal();
  factory BehavioralClassifier() => _instance;
  BehavioralClassifier._internal();

  // 🚀 User behavioral baselines (simulating trained model)
  final Map<String, UserBehavioralProfile> _userProfiles = {};
  
  // 🚀 Real-time classification thresholds
  static const double _authenticationThreshold = 0.6;
  static const double _challengeThreshold = 0.3;
  static const double _anomalyThreshold = 0.2;

  /// 🚀 HACKATHON: Classify user behavior in real-time
  Future<BehavioralClassification> classifyBehavior(
    String userId,
    BehavioralData currentData,
  ) async {
    // Simulate ML processing time
    await Future.delayed(const Duration(milliseconds: 300));

    final profile = _getUserProfile(userId);
    final features = _extractFeatures(currentData);
    final score = _calculateSimilarityScore(profile, features);
    
    return BehavioralClassification(
      userId: userId,
      score: score,
      confidence: _calculateConfidence(score, features),
      features: features,
      classification: _determineClassification(score),
      anomalies: _detectAnomalies(profile, features),
      recommendation: _getRecommendation(score),
    );
  }

  /// 🚀 Extract behavioral features from raw data
  BehavioralFeatures _extractFeatures(BehavioralData data) {
    final features = BehavioralFeatures();

    // Keystroke dynamics
    if (data.keystrokeTimes.length > 2) {
      final intervals = <int>[];
      for (int i = 1; i < data.keystrokeTimes.length; i++) {
        intervals.add(data.keystrokeTimes[i] - data.keystrokeTimes[i - 1]);
      }
      
      features.avgKeystrokeInterval = intervals.reduce((a, b) => a + b) / intervals.length;
      features.keystrokeVariance = _calculateVariance(intervals.map((i) => i.toDouble()).toList());
      features.keystrokeRhythm = _calculateRhythmScore(intervals);
    }

    // Typing speed
    features.typingSpeed = data.typingSpeedKPM;

    // Touch patterns
    if (data.tapPositions.isNotEmpty) {
      features.avgTouchPressure = data.tapPressures.isNotEmpty 
          ? data.tapPressures.reduce((a, b) => a + b) / data.tapPressures.length 
          : 0.0;
      
      features.touchVariance = _calculateTouchVariance(data.tapPositions);
      features.touchPattern = _analyzeTouchPattern(data.tapPositions);
    }

    // Temporal patterns
    features.sessionDuration = DateTime.now().millisecondsSinceEpoch - data.sessionStartTime;
    features.timeOfDay = DateTime.now().hour;
    features.dayOfWeek = DateTime.now().weekday;

    // Behavioral consistency
    features.behavioralConsistency = _calculateConsistency(data);

    return features;
  }

  /// 🚀 Calculate similarity score against user profile
  double _calculateSimilarityScore(UserBehavioralProfile profile, BehavioralFeatures features) {
    double score = 0.0;
    int weightSum = 0;

    // Keystroke timing similarity (weight: 30%)
    if (profile.avgKeystrokeInterval > 0 && features.avgKeystrokeInterval > 0) {
      final timingSimilarity = 1.0 - ((profile.avgKeystrokeInterval - features.avgKeystrokeInterval).abs() / profile.avgKeystrokeInterval).clamp(0.0, 1.0);
      score += timingSimilarity * 0.3;
      weightSum += 30;
    }

    // Typing speed similarity (weight: 25%)
    if (profile.typingSpeed > 0 && features.typingSpeed > 0) {
      final speedSimilarity = 1.0 - ((profile.typingSpeed - features.typingSpeed).abs() / profile.typingSpeed).clamp(0.0, 1.0);
      score += speedSimilarity * 0.25;
      weightSum += 25;
    }

    // Touch pressure similarity (weight: 20%)
    if (profile.avgTouchPressure > 0 && features.avgTouchPressure > 0) {
      final pressureSimilarity = 1.0 - ((profile.avgTouchPressure - features.avgTouchPressure).abs() / profile.avgTouchPressure).clamp(0.0, 1.0);
      score += pressureSimilarity * 0.2;
      weightSum += 20;
    }

    // Rhythm consistency (weight: 15%)
    if (profile.keystrokeRhythm > 0 && features.keystrokeRhythm > 0) {
      final rhythmSimilarity = 1.0 - ((profile.keystrokeRhythm - features.keystrokeRhythm).abs() / profile.keystrokeRhythm).clamp(0.0, 1.0);
      score += rhythmSimilarity * 0.15;
      weightSum += 15;
    }

    // Time of day pattern (weight: 10%)
    if (profile.commonTimeOfDay.contains(features.timeOfDay)) {
      score += 0.1;
      weightSum += 10;
    }

    return weightSum > 0 ? score / (weightSum / 100) : 0.0;
  }

  /// 🚀 Determine classification based on score
  BehavioralClass _determineClassification(double score) {
    if (score >= _authenticationThreshold) {
      return BehavioralClass.authenticated;
    } else if (score >= _challengeThreshold) {
      return BehavioralClass.requiresChallenge;
    } else if (score >= _anomalyThreshold) {
      return BehavioralClass.suspicious;
    } else {
      return BehavioralClass.unauthorized;
    }
  }

  /// 🚀 Detect behavioral anomalies
  List<BehavioralAnomaly> _detectAnomalies(UserBehavioralProfile profile, BehavioralFeatures features) {
    final anomalies = <BehavioralAnomaly>[];

    // Typing speed anomaly
    if (profile.typingSpeed > 0) {
      final speedDiff = (features.typingSpeed - profile.typingSpeed).abs();
      if (speedDiff > profile.typingSpeed * 0.5) {
        anomalies.add(BehavioralAnomaly(
          type: AnomalyType.typingSpeed,
          severity: speedDiff > profile.typingSpeed ? AnomalySeverity.high : AnomalySeverity.medium,
          description: 'Typing speed significantly different from baseline',
          expectedValue: profile.typingSpeed,
          actualValue: features.typingSpeed,
        ));
      }
    }

    // Keystroke rhythm anomaly
    if (profile.keystrokeRhythm > 0) {
      final rhythmDiff = (features.keystrokeRhythm - profile.keystrokeRhythm).abs();
      if (rhythmDiff > 0.3) {
        anomalies.add(BehavioralAnomaly(
          type: AnomalyType.keystrokeRhythm,
          severity: rhythmDiff > 0.5 ? AnomalySeverity.high : AnomalySeverity.medium,
          description: 'Keystroke rhythm pattern unusual',
          expectedValue: profile.keystrokeRhythm,
          actualValue: features.keystrokeRhythm,
        ));
      }
    }

    // Time of day anomaly
    if (!profile.commonTimeOfDay.contains(features.timeOfDay)) {
      anomalies.add(BehavioralAnomaly(
        type: AnomalyType.timeOfDay,
        severity: AnomalySeverity.low,
        description: 'Unusual time of day for banking activity',
        expectedValue: profile.commonTimeOfDay.join(','),
        actualValue: features.timeOfDay.toDouble(),
      ));
    }

    return anomalies;
  }

  /// 🚀 Get authentication recommendation
  AuthRecommendation _getRecommendation(double score) {
    if (score >= 0.8) {
      return AuthRecommendation.allowImmediate;
    } else if (score >= 0.6) {
      return AuthRecommendation.allowWithMonitoring;
    } else if (score >= 0.3) {
      return AuthRecommendation.requireChallenge;
    } else {
      return AuthRecommendation.denyAccess;
    }
  }

  /// 🚀 Get or create user behavioral profile
  UserBehavioralProfile _getUserProfile(String userId) {
    if (!_userProfiles.containsKey(userId)) {
      _userProfiles[userId] = _createDefaultProfile(userId);
    }
    return _userProfiles[userId]!;
  }

  /// 🚀 Create default profile for new users
  UserBehavioralProfile _createDefaultProfile(String userId) {
    final random = Random();
    return UserBehavioralProfile(
      userId: userId,
      avgKeystrokeInterval: (150 + random.nextInt(100)).toDouble(), // 150-250ms
      typingSpeed: (40 + random.nextInt(40)).toDouble(), // 40-80 WPM
      avgTouchPressure: 50 + random.nextDouble() * 100, // 50-150
      keystrokeRhythm: 0.6 + random.nextDouble() * 0.3, // 0.6-0.9
      touchPattern: random.nextDouble(),
      commonTimeOfDay: [9, 10, 11, 14, 15, 16, 19, 20], // Common banking hours
      sessionCount: 0,
      lastUpdated: DateTime.now(),
    );
  }

  /// 🚀 Update user profile with new behavioral data
  Future<void> updateUserProfile(String userId, BehavioralData data) async {
    final profile = _getUserProfile(userId);
    final features = _extractFeatures(data);

    // Update profile with weighted average (simulating online learning)
    final weight = 0.1; // Learning rate
    
    if (features.avgKeystrokeInterval > 0) {
      profile.avgKeystrokeInterval = profile.avgKeystrokeInterval * (1 - weight) + features.avgKeystrokeInterval * weight;
    }
    
    if (features.typingSpeed > 0) {
      profile.typingSpeed = profile.typingSpeed * (1 - weight) + features.typingSpeed * weight;
    }
    
    if (features.avgTouchPressure > 0) {
      profile.avgTouchPressure = profile.avgTouchPressure * (1 - weight) + features.avgTouchPressure * weight;
    }
    
    if (features.keystrokeRhythm > 0) {
      profile.keystrokeRhythm = profile.keystrokeRhythm * (1 - weight) + features.keystrokeRhythm * weight;
    }

    // Update time of day patterns
    if (!profile.commonTimeOfDay.contains(features.timeOfDay)) {
      profile.commonTimeOfDay.add(features.timeOfDay);
      if (profile.commonTimeOfDay.length > 12) {
        profile.commonTimeOfDay.removeAt(0); // Keep only recent patterns
      }
    }

    profile.sessionCount++;
    profile.lastUpdated = DateTime.now();
  }

  /// 🚀 Utility functions for feature calculation
  double _calculateVariance(List<double> values) {
    if (values.isEmpty) return 0.0;
    final mean = values.reduce((a, b) => a + b) / values.length;
    final variance = values.map((x) => (x - mean) * (x - mean)).reduce((a, b) => a + b) / values.length;
    return variance;
  }

  double _calculateRhythmScore(List<int> intervals) {
    if (intervals.length < 3) return 0.5;
    
    var consistencyScore = 0.0;
    for (int i = 1; i < intervals.length; i++) {
      final ratio = intervals[i] / intervals[i - 1];
      if (ratio > 0.7 && ratio < 1.3) { // Within 30% of previous interval
        consistencyScore += 1.0;
      }
    }
    
    return consistencyScore / (intervals.length - 1);
  }

  double _calculateTouchVariance(List<Offset?> positions) {
    if (positions.length < 2) return 0.0;
    
    final distances = <double>[];
    for (int i = 1; i < positions.length; i++) {
      final current = positions[i];
      final previous = positions[i - 1];
      if (current != null && previous != null) {
        distances.add((current - previous).distance);
      }
    }
    
    return _calculateVariance(distances);
  }

  double _analyzeTouchPattern(List<Offset?> positions) {
    if (positions.isEmpty) return 0.0;
    
    // Calculate center of mass
    final validPositions = positions.where((p) => p != null).cast<Offset>().toList();
    if (validPositions.isEmpty) return 0.0;

    final centerX = validPositions.map((p) => p.dx).reduce((a, b) => a + b) / validPositions.length;
    final centerY = validPositions.map((p) => p.dy).reduce((a, b) => a + b) / validPositions.length;

    // Calculate spread from center
    final spreads = validPositions.map((p) => (p - Offset(centerX, centerY)).distance).toList();
    return spreads.reduce((a, b) => a + b) / spreads.length;
  }

  double _calculateConsistency(BehavioralData data) {
    var consistencyScore = 0.0;
    int factors = 0;

    // Keystroke consistency
    if (data.keystrokeTimes.length > 2) {
      final rhythmScore = _calculateRhythmScore(
        data.keystrokeTimes.map((t) => t).toList().sublist(1).asMap().entries
            .map((e) => data.keystrokeTimes[e.key + 1] - data.keystrokeTimes[e.key])
            .toList(),
      );
      consistencyScore += rhythmScore;
      factors++;
    }

    // Touch pressure consistency
    if (data.tapPressures.length > 1) {
      final variance = _calculateVariance(data.tapPressures);
      final mean = data.tapPressures.reduce((a, b) => a + b) / data.tapPressures.length;
      final consistencyScore2 = 1.0 / (1.0 + variance / (mean * mean));
      consistencyScore += consistencyScore2;
      factors++;
    }

    return factors > 0 ? consistencyScore / factors : 0.5;
  }

  double _calculateConfidence(double score, BehavioralFeatures features) {
    // Base confidence on score
    var confidence = score;
    
    // Increase confidence with more data points
    if (features.avgKeystrokeInterval > 0) confidence += 0.1;
    if (features.typingSpeed > 0) confidence += 0.1;
    if (features.avgTouchPressure > 0) confidence += 0.1;
    if (features.behavioralConsistency > 0.5) confidence += 0.1;
    
    return confidence.clamp(0.0, 1.0);
  }

  /// 🚀 Generate training data for demo purposes
  void generateDemoProfiles() {
    final demoUsers = ['demo_user_1', 'demo_user_2', 'canara_demo'];
    for (final userId in demoUsers) {
      _userProfiles[userId] = _createDefaultProfile(userId);
    }
  }
}

// 🚀 HACKATHON: Behavioral Classification Models

enum BehavioralClass {
  authenticated,
  requiresChallenge,
  suspicious,
  unauthorized,
}

enum AuthRecommendation {
  allowImmediate,
  allowWithMonitoring,
  requireChallenge,
  denyAccess,
}

enum AnomalyType {
  typingSpeed,
  keystrokeRhythm,
  touchPressure,
  timeOfDay,
  sessionDuration,
}

class BehavioralFeatures {
  double avgKeystrokeInterval = 0.0;
  double keystrokeVariance = 0.0;
  double keystrokeRhythm = 0.0;
  double typingSpeed = 0.0;
  double avgTouchPressure = 0.0;
  double touchVariance = 0.0;
  double touchPattern = 0.0;
  int sessionDuration = 0;
  int timeOfDay = 0;
  int dayOfWeek = 0;
  double behavioralConsistency = 0.0;

  Map<String, dynamic> toJson() {
    return {
      'avg_keystroke_interval': avgKeystrokeInterval,
      'keystroke_variance': keystrokeVariance,
      'keystroke_rhythm': keystrokeRhythm,
      'typing_speed': typingSpeed,
      'avg_touch_pressure': avgTouchPressure,
      'touch_variance': touchVariance,
      'touch_pattern': touchPattern,
      'session_duration': sessionDuration,
      'time_of_day': timeOfDay,
      'day_of_week': dayOfWeek,
      'behavioral_consistency': behavioralConsistency,
    };
  }
}

class UserBehavioralProfile {
  final String userId;
  double avgKeystrokeInterval;
  double typingSpeed;
  double avgTouchPressure;
  double keystrokeRhythm;
  double touchPattern;
  List<int> commonTimeOfDay;
  int sessionCount;
  DateTime lastUpdated;

  UserBehavioralProfile({
    required this.userId,
    required this.avgKeystrokeInterval,
    required this.typingSpeed,
    required this.avgTouchPressure,
    required this.keystrokeRhythm,
    required this.touchPattern,
    required this.commonTimeOfDay,
    required this.sessionCount,
    required this.lastUpdated,
  });

  Map<String, dynamic> toJson() {
    return {
      'user_id': userId,
      'avg_keystroke_interval': avgKeystrokeInterval,
      'typing_speed': typingSpeed,
      'avg_touch_pressure': avgTouchPressure,
      'keystroke_rhythm': keystrokeRhythm,
      'touch_pattern': touchPattern,
      'common_time_of_day': commonTimeOfDay,
      'session_count': sessionCount,
      'last_updated': lastUpdated.toIso8601String(),
    };
  }
}

class BehavioralClassification {
  final String userId;
  final double score;
  final double confidence;
  final BehavioralFeatures features;
  final BehavioralClass classification;
  final List<BehavioralAnomaly> anomalies;
  final AuthRecommendation recommendation;

  BehavioralClassification({
    required this.userId,
    required this.score,
    required this.confidence,
    required this.features,
    required this.classification,
    required this.anomalies,
    required this.recommendation,
  });

  Map<String, dynamic> toJson() {
    return {
      'user_id': userId,
      'score': score,
      'confidence': confidence,
      'features': features.toJson(),
      'classification': classification.toString(),
      'anomalies': anomalies.map((a) => a.toJson()).toList(),
      'recommendation': recommendation.toString(),
      'timestamp': DateTime.now().toIso8601String(),
    };
  }
}

class BehavioralAnomaly {
  final AnomalyType type;
  final AnomalySeverity severity;
  final String description;
  final dynamic expectedValue;
  final dynamic actualValue;

  BehavioralAnomaly({
    required this.type,
    required this.severity,
    required this.description,
    required this.expectedValue,
    required this.actualValue,
  });

  Map<String, dynamic> toJson() {
    return {
      'type': type.toString(),
      'severity': severity.toString(),
      'description': description,
      'expected_value': expectedValue,
      'actual_value': actualValue,
      'timestamp': DateTime.now().toIso8601String(),
    };
  }
}

// Import the AnomalySeverity from panic_detector.dart or define it here
enum AnomalySeverity {
  low,
  medium,
  high,
  critical,
}
