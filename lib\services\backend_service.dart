import 'dart:async';
import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../firebase/firebase_service.dart';
import '../services/auth_service.dart';
import '../services/transaction_service.dart';
import '../services/enhanced_security_service.dart';
import '../services/production_ml_engine.dart';
import '../models/transaction_model.dart';
import '../models/behavioral_data_model.dart';

class BackendService {
  static final BackendService _instance = BackendService._internal();
  factory BackendService() => _instance;
  BackendService._internal();

  // Services
  final FirebaseService _firebaseService = FirebaseService();
  final TransactionService _transactionService = TransactionService();
  final EnhancedSecurityService _securityService = EnhancedSecurityService();
  final ProductionMLEngine _mlEngine = ProductionMLEngine();

  // User profile management
  UserProfile? _currentUserProfile;
  bool _isInitialized = false;
  Timer? _syncTimer;

  // Real-time synchronization
  bool _realTimeSyncEnabled = true;
  StreamController<BackendEvent>? _eventController;
  Stream<BackendEvent>? _eventStream;

  /// Initialize backend service with real-time sync
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Initialize event stream
      _eventController = StreamController<BackendEvent>.broadcast();
      _eventStream = _eventController!.stream;

      // Initialize ML engine
      await _mlEngine.initialize();

      // Initialize security service
      await _securityService.initialize(
        onSecurityAlert: _handleSecurityAlert,
        onAnomalyDetected: _handleAnomalyDetected,
      );

      // Load user profile
      await _loadUserProfile();

      // Start real-time synchronization
      if (_realTimeSyncEnabled) {
        _startRealTimeSync();
      }

      _isInitialized = true;
      _emitEvent(BackendEvent(
        type: BackendEventType.initialized,
        message: 'Backend service initialized successfully',
        timestamp: DateTime.now(),
      ));

      print('🚀 Backend service initialized successfully');
    } catch (e) {
      print('❌ Failed to initialize backend service: $e');
      throw BackendException('Failed to initialize backend service: $e');
    }
  }

  /// Create or update user profile
  Future<UserProfile> createUserProfile({
    required String userId,
    required String name,
    required String email,
    required String phoneNumber,
    Map<String, dynamic>? additionalData,
  }) async {
    try {
      final profile = UserProfile(
        userId: userId,
        name: name,
        email: email,
        phoneNumber: phoneNumber,
        createdAt: DateTime.now(),
        lastUpdated: DateTime.now(),
        isActive: true,
        securityLevel: SecurityLevel.standard,
        preferences: UserPreferences(),
        additionalData: additionalData ?? {},
      );

      // Save locally
      await _saveUserProfile(profile);

      // Upload to Firebase
      await _firebaseService.uploadBehavioralData(
        userId,
        BehavioralData()..usageTimeOfDay = 'PROFILE_CREATION',
        geolocation: await _getCurrentLocation(),
      );

      // Store profile data in Firebase
      await _uploadUserProfile(profile);

      _currentUserProfile = profile;

      _emitEvent(BackendEvent(
        type: BackendEventType.profileCreated,
        message: 'User profile created successfully',
        timestamp: DateTime.now(),
        data: profile.toJson(),
      ));

      return profile;
    } catch (e) {
      print('❌ Failed to create user profile: $e');
      throw BackendException('Failed to create user profile: $e');
    }
  }

  /// Get current user profile
  Future<UserProfile?> getCurrentUserProfile() async {
    if (_currentUserProfile != null) {
      return _currentUserProfile;
    }

    return await _loadUserProfile();
  }

  /// Update user profile
  Future<void> updateUserProfile(UserProfile profile) async {
    try {
      profile.lastUpdated = DateTime.now();
      
      await _saveUserProfile(profile);
      await _uploadUserProfile(profile);
      
      _currentUserProfile = profile;

      _emitEvent(BackendEvent(
        type: BackendEventType.profileUpdated,
        message: 'User profile updated successfully',
        timestamp: DateTime.now(),
        data: profile.toJson(),
      ));
    } catch (e) {
      print('❌ Failed to update user profile: $e');
      throw BackendException('Failed to update user profile: $e');
    }
  }

  /// Process transaction with ML analysis
  Future<TransactionResult> processTransaction({
    required Transaction transaction,
    required BehavioralData behavioralData,
  }) async {
    try {
      // Perform ML-based fraud detection
      final fraudAnalysis = await _mlEngine.analyzeTransactionRisk(
        transaction.amount,
        transaction.category.toString(),
        behavioralData,
      );

      // Check security status
      final securityStatus = _securityService.getCurrentSecurityStatus();

      // Determine if transaction should be approved
      bool isApproved = fraudAnalysis.riskScore < 0.7 && 
                       securityStatus.threatLevel != SecurityThreatLevel.critical;

      if (isApproved) {
        // Add transaction
        await _transactionService.addTransaction(transaction);

        // Update ML models with successful transaction
        await _mlEngine.updateUserProfile(
          _currentUserProfile?.userId ?? 'unknown',
          behavioralData,
        );
      }

      final result = TransactionResult(
        transaction: transaction,
        isApproved: isApproved,
        riskScore: fraudAnalysis.riskScore,
        fraudAnalysis: fraudAnalysis,
        securityStatus: securityStatus,
        timestamp: DateTime.now(),
      );

      _emitEvent(BackendEvent(
        type: BackendEventType.transactionProcessed,
        message: isApproved ? 'Transaction approved' : 'Transaction blocked',
        timestamp: DateTime.now(),
        data: result.toJson(),
      ));

      return result;
    } catch (e) {
      print('❌ Failed to process transaction: $e');
      throw BackendException('Failed to process transaction: $e');
    }
  }

  /// Perform behavioral authentication
  Future<AuthenticationResult> performBehavioralAuth({
    required String userId,
    required BehavioralData behavioralData,
  }) async {
    try {
      // Use ML engine for authentication
      final authResult = await _mlEngine.authenticateUser(userId, behavioralData);

      // Update security status
      final securityStatus = _securityService.getCurrentSecurityStatus();

      final result = AuthenticationResult(
        isAuthenticated: authResult.isAuthenticated,
        confidence: authResult.confidenceScore,
        riskLevel: authResult.riskLevel,
        securityStatus: securityStatus,
        timestamp: DateTime.now(),
        behavioralFeatures: authResult.behavioralFeatures,
      );

      _emitEvent(BackendEvent(
        type: BackendEventType.authenticationCompleted,
        message: authResult.isAuthenticated ? 'Authentication successful' : 'Authentication failed',
        timestamp: DateTime.now(),
        data: result.toJson(),
      ));

      return result;
    } catch (e) {
      print('❌ Failed to perform behavioral authentication: $e');
      throw BackendException('Failed to perform behavioral authentication: $e');
    }
  }

  /// Start real-time synchronization
  void _startRealTimeSync() {
    _syncTimer = Timer.periodic(const Duration(seconds: 30), (timer) async {
      await _performSync();
    });
  }

  /// Perform data synchronization
  Future<void> _performSync() async {
    try {
      if (_currentUserProfile == null) return;

      // Sync transactions
      final transactions = _transactionService.getAllTransactions();
      for (final transaction in transactions) {
        await _firebaseService.uploadTransactionData(
          _currentUserProfile!.userId,
          transaction.toJson(),
        );
      }

      // Sync security status
      final securityStatus = _securityService.getCurrentSecurityStatus();
      await _firebaseService.uploadBehavioralData(
        _currentUserProfile!.userId,
        BehavioralData()..usageTimeOfDay = 'SYNC',
        geolocation: securityStatus.lastLocationUpdate,
      );

      _emitEvent(BackendEvent(
        type: BackendEventType.syncCompleted,
        message: 'Data synchronization completed',
        timestamp: DateTime.now(),
      ));
    } catch (e) {
      print('❌ Sync failed: $e');
    }
  }

  /// Handle security alerts
  void _handleSecurityAlert(SecurityAlert alert) {
    _emitEvent(BackendEvent(
      type: BackendEventType.securityAlert,
      message: alert.message,
      timestamp: alert.timestamp,
      data: {
        'alertType': alert.type.toString(),
        'severity': alert.severity.toString(),
        'location': alert.location,
        'recommendedAction': alert.recommendedAction,
      },
    ));
  }

  /// Handle anomaly detection
  void _handleAnomalyDetected(SecurityAnomaly anomaly) {
    _emitEvent(BackendEvent(
      type: BackendEventType.anomalyDetected,
      message: anomaly.description,
      timestamp: anomaly.timestamp,
      data: {
        'anomalyType': anomaly.type.toString(),
        'severity': anomaly.severity.toString(),
        'confidence': anomaly.confidence,
      },
    ));
  }

  /// Load user profile from local storage
  Future<UserProfile?> _loadUserProfile() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final profileJson = prefs.getString('user_profile');
      
      if (profileJson != null) {
        final profileData = jsonDecode(profileJson);
        _currentUserProfile = UserProfile.fromJson(profileData);
        return _currentUserProfile;
      }
    } catch (e) {
      print('Error loading user profile: $e');
    }
    return null;
  }

  /// Save user profile to local storage
  Future<void> _saveUserProfile(UserProfile profile) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('user_profile', jsonEncode(profile.toJson()));
    } catch (e) {
      print('Error saving user profile: $e');
    }
  }

  /// Upload user profile to Firebase
  Future<void> _uploadUserProfile(UserProfile profile) async {
    try {
      await _firebaseService.uploadBehavioralData(
        profile.userId,
        BehavioralData()..usageTimeOfDay = 'PROFILE_UPDATE',
        geolocation: await _getCurrentLocation(),
      );
    } catch (e) {
      print('Error uploading user profile: $e');
    }
  }

  /// Get current location
  Future<Map<String, dynamic>?> _getCurrentLocation() async {
    try {
      final securityStatus = _securityService.getCurrentSecurityStatus();
      return securityStatus.lastLocationUpdate;
    } catch (e) {
      return null;
    }
  }

  /// Emit backend event
  void _emitEvent(BackendEvent event) {
    _eventController?.add(event);
  }

  /// Get event stream
  Stream<BackendEvent>? get eventStream => _eventStream;

  /// Enable/disable real-time sync
  void setRealTimeSyncEnabled(bool enabled) {
    _realTimeSyncEnabled = enabled;
    if (enabled && _isInitialized) {
      _startRealTimeSync();
    } else {
      _syncTimer?.cancel();
    }
  }

  /// Dispose backend service
  void dispose() {
    _syncTimer?.cancel();
    _eventController?.close();
    _securityService.dispose();
    _isInitialized = false;
  }
}

// Supporting classes
enum BackendEventType {
  initialized,
  profileCreated,
  profileUpdated,
  transactionProcessed,
  authenticationCompleted,
  securityAlert,
  anomalyDetected,
  syncCompleted,
  error,
}

enum SecurityLevel { basic, standard, enhanced, premium }

class BackendEvent {
  final BackendEventType type;
  final String message;
  final DateTime timestamp;
  final Map<String, dynamic>? data;

  BackendEvent({
    required this.type,
    required this.message,
    required this.timestamp,
    this.data,
  });
}

class BackendException implements Exception {
  final String message;
  BackendException(this.message);
  
  @override
  String toString() => 'BackendException: $message';
}

class UserProfile {
  final String userId;
  final String name;
  final String email;
  final String phoneNumber;
  final DateTime createdAt;
  DateTime lastUpdated;
  final bool isActive;
  final SecurityLevel securityLevel;
  final UserPreferences preferences;
  final Map<String, dynamic> additionalData;

  UserProfile({
    required this.userId,
    required this.name,
    required this.email,
    required this.phoneNumber,
    required this.createdAt,
    required this.lastUpdated,
    required this.isActive,
    required this.securityLevel,
    required this.preferences,
    required this.additionalData,
  });

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'name': name,
      'email': email,
      'phoneNumber': phoneNumber,
      'createdAt': createdAt.toIso8601String(),
      'lastUpdated': lastUpdated.toIso8601String(),
      'isActive': isActive,
      'securityLevel': securityLevel.toString(),
      'preferences': preferences.toJson(),
      'additionalData': additionalData,
    };
  }

  factory UserProfile.fromJson(Map<String, dynamic> json) {
    return UserProfile(
      userId: json['userId'],
      name: json['name'],
      email: json['email'],
      phoneNumber: json['phoneNumber'],
      createdAt: DateTime.parse(json['createdAt']),
      lastUpdated: DateTime.parse(json['lastUpdated']),
      isActive: json['isActive'],
      securityLevel: SecurityLevel.values.firstWhere(
        (e) => e.toString() == json['securityLevel'],
        orElse: () => SecurityLevel.standard,
      ),
      preferences: UserPreferences.fromJson(json['preferences'] ?? {}),
      additionalData: json['additionalData'] ?? {},
    );
  }
}

class UserPreferences {
  bool biometricEnabled;
  bool notificationsEnabled;
  bool locationTrackingEnabled;
  String preferredLanguage;

  UserPreferences({
    this.biometricEnabled = true,
    this.notificationsEnabled = true,
    this.locationTrackingEnabled = true,
    this.preferredLanguage = 'en',
  });

  Map<String, dynamic> toJson() {
    return {
      'biometricEnabled': biometricEnabled,
      'notificationsEnabled': notificationsEnabled,
      'locationTrackingEnabled': locationTrackingEnabled,
      'preferredLanguage': preferredLanguage,
    };
  }

  factory UserPreferences.fromJson(Map<String, dynamic> json) {
    return UserPreferences(
      biometricEnabled: json['biometricEnabled'] ?? true,
      notificationsEnabled: json['notificationsEnabled'] ?? true,
      locationTrackingEnabled: json['locationTrackingEnabled'] ?? true,
      preferredLanguage: json['preferredLanguage'] ?? 'en',
    );
  }
}

class TransactionResult {
  final Transaction transaction;
  final bool isApproved;
  final double riskScore;
  final dynamic fraudAnalysis;
  final dynamic securityStatus;
  final DateTime timestamp;

  TransactionResult({
    required this.transaction,
    required this.isApproved,
    required this.riskScore,
    required this.fraudAnalysis,
    required this.securityStatus,
    required this.timestamp,
  });

  Map<String, dynamic> toJson() {
    return {
      'transaction': transaction.toJson(),
      'isApproved': isApproved,
      'riskScore': riskScore,
      'timestamp': timestamp.toIso8601String(),
    };
  }
}

class AuthenticationResult {
  final bool isAuthenticated;
  final double confidence;
  final String riskLevel;
  final dynamic securityStatus;
  final DateTime timestamp;
  final Map<String, double> behavioralFeatures;

  AuthenticationResult({
    required this.isAuthenticated,
    required this.confidence,
    required this.riskLevel,
    required this.securityStatus,
    required this.timestamp,
    required this.behavioralFeatures,
  });

  Map<String, dynamic> toJson() {
    return {
      'isAuthenticated': isAuthenticated,
      'confidence': confidence,
      'riskLevel': riskLevel,
      'timestamp': timestamp.toIso8601String(),
      'behavioralFeatures': behavioralFeatures,
    };
  }
}
