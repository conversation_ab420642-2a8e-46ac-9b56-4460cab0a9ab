import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../widgets/app_widgets.dart';
import '../services/auth_service.dart';
import '../services/security_service.dart';
import '../routes/app_routes.dart';
import '../utils/constants.dart' as constants;

class SecureLoginScreen extends StatefulWidget {
  const SecureLoginScreen({Key? key}) : super(key: key);

  @override
  State<SecureLoginScreen> createState() => _SecureLoginScreenState();
}

class _SecureLoginScreenState extends State<SecureLoginScreen>
    with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  
  bool _isPasswordVisible = false;
  bool _isLoading = false;
  bool _rememberMe = false;
  
  // Behavioral tracking
  DateTime? _typingStartTime;
  List<int> _keystrokeTiming = [];
  double _averageTypingSpeed = 0.0;
  
  // Animation controllers
  late AnimationController _shakeController;
  late Animation<double> _shakeAnimation;
  
  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _setupBehavioralTracking();
  }
  
  void _setupAnimations() {
    _shakeController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    
    _shakeAnimation = Tween<double>(
      begin: 0,
      end: 10,
    ).animate(CurvedAnimation(
      parent: _shakeController,
      curve: Curves.elasticIn,
    ));
  }
  
  void _setupBehavioralTracking() {
    _emailController.addListener(_trackTypingBehavior);
    _passwordController.addListener(_trackTypingBehavior);
  }
  
  void _trackTypingBehavior() {
    final currentTime = DateTime.now();
    
    if (_typingStartTime == null) {
      _typingStartTime = currentTime;
      return;
    }
    
    final timeDiff = currentTime.difference(_typingStartTime!).inMilliseconds;
    _keystrokeTiming.add(timeDiff);
    _typingStartTime = currentTime;
    
    // Calculate average typing speed
    if (_keystrokeTiming.length > 1) {
      final totalTime = _keystrokeTiming.reduce((a, b) => a + b);
      _averageTypingSpeed = _keystrokeTiming.length / (totalTime / 1000);
    }
  }
  
  Future<void> _handleLogin() async {
    if (!_formKey.currentState!.validate()) {
      _shakeController.forward().then((_) => _shakeController.reverse());
      return;
    }
    
    setState(() => _isLoading = true);
    
    try {
      // Record behavioral data
      await _recordBehavioralData();
      
      // Attempt login
      final success = await AuthService.login(
        _emailController.text.trim(),
        _passwordController.text,
      );
      
      if (success) {
        // Calculate trust score
        final trustScore = await SecurityService.calculateTrustScore();
        
        if (trustScore < 60) {
          // Require additional verification
          Navigator.pushReplacementNamed(context, AppRoutes.behavioralAuth);
        } else {
          // Direct to dashboard
          Navigator.pushReplacementNamed(context, AppRoutes.dashboard);
        }
      } else {
        _showError('Invalid email or password');
        _shakeController.forward().then((_) => _shakeController.reverse());
      }
    } catch (e) {
      _showError('Login failed. Please try again.');
    } finally {
      setState(() => _isLoading = false);
    }
  }
  
  Future<void> _recordBehavioralData() async {
    final behavioralData = {
      'typing_speed': _averageTypingSpeed,
      'keystroke_timing': _keystrokeTiming,
      'login_time': DateTime.now().hour,
      'form_completion_time': DateTime.now().difference(_typingStartTime ?? DateTime.now()).inSeconds,
    };
    
    await SecurityService.recordBehavioralData(behavioralData);
  }
  
  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: constants.AppColors.debitRed,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );
  }

  void _showPasswordResetInfo(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: const Color(0xFF1E293B),
          title: const Row(
            children: [
              Icon(Icons.info_outline, color: Color(0xFF3B82F6)),
              SizedBox(width: 8),
              Text(
                'Password Reset Information',
                style: TextStyle(color: Colors.white, fontSize: 18),
              ),
            ],
          ),
          content: const Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'For security reasons, banking passwords cannot be reset online.',
                style: TextStyle(color: Colors.white70, fontSize: 14),
              ),
              SizedBox(height: 12),
              Text(
                'To reset your password, please:',
                style: TextStyle(color: Colors.white, fontSize: 14, fontWeight: FontWeight.w600),
              ),
              SizedBox(height: 8),
              Text(
                '• Visit your nearest Trust Chain Bank branch\n• Bring valid government ID\n• Fill out password reset form\n• Verify your identity with bank officials',
                style: TextStyle(color: Colors.white70, fontSize: 13),
              ),
              SizedBox(height: 12),
              Text(
                'Customer Care: 1800-123-4567',
                style: TextStyle(color: Color(0xFF3B82F6), fontSize: 14, fontWeight: FontWeight.w600),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text(
                'Understood',
                style: TextStyle(color: Color(0xFF3B82F6)),
              ),
            ),
          ],
        );
      },
    );
  }

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    _shakeController.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: constants.AppColors.bankingBackground,
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              constants.AppColors.bankingBackground,
              constants.AppColors.bankingSurface,
            ],
          ),
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(24),
            child: AnimatedBuilder(
              animation: _shakeAnimation,
              builder: (context, child) {
                return Transform.translate(
                  offset: Offset(_shakeAnimation.value, 0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SizedBox(height: 40),
                      
                      // Header
                      _buildHeader(),
                      const SizedBox(height: 48),
                      
                      // Login Form
                      _buildLoginForm(),
                      const SizedBox(height: 32),
                      
                      // Login Button
                      _buildLoginButton(),
                      const SizedBox(height: 24),
                      
                      // Additional Options
                      _buildAdditionalOptions(),
                      const SizedBox(height: 32),
                      
                      // Security Notice
                      _buildSecurityNotice(),
                    ],
                  ),
                );
              },
            ),
          ),
        ),
      ),
    );
  }
  
  Widget _buildHeader() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Logo
        Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: LinearGradient(
              colors: [AppColors.primary, AppColors.primaryDark],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
          child: const Icon(
            Icons.account_balance,
            size: 40,
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 24),
        
        // Welcome text
        Text(
          'Welcome Back',
          style: AppTextStyles.heading1.copyWith(
            fontSize: 28,
            fontWeight: FontWeight.w800,
          ),
        ),
        const SizedBox(height: 8),
        
        Text(
          'Sign in to access your secure banking',
          style: AppTextStyles.bodyLarge.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
      ],
    );
  }
  
  Widget _buildLoginForm() {
    return Form(
      key: _formKey,
      child: Column(
        children: [
          // Email Field
          _buildEmailField(),
          const SizedBox(height: 20),
          
          // Password Field
          _buildPasswordField(),
          const SizedBox(height: 16),
          
          // Remember Me & Forgot Password
          Row(
            children: [
              Expanded(
                child: Row(
                  children: [
                    Checkbox(
                      value: _rememberMe,
                      onChanged: (value) => setState(() => _rememberMe = value ?? false),
                      activeColor: AppColors.primary,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                    Text(
                      'Remember me',
                      style: AppTextStyles.bodyMedium.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
              GestureDetector(
                onTap: () {
                  _showPasswordResetInfo(context);
                },
                child: const Text(
                  'Password Help?',
                  style: TextStyle(
                    color: Color(0xFF3B82F6),
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
  
  Widget _buildEmailField() {
    return TextFormField(
      controller: _emailController,
      keyboardType: TextInputType.emailAddress,
      style: AppTextStyles.bodyLarge,
      decoration: InputDecoration(
        labelText: 'Email Address',
        labelStyle: AppTextStyles.bodyMedium.copyWith(
          color: AppColors.textSecondary,
        ),
        prefixIcon: Icon(
          Icons.email_outlined,
          color: AppColors.textSecondary,
        ),
        filled: true,
        fillColor: AppColors.surfaceVariant,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide.none,
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: AppColors.primary, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: AppColors.error, width: 2),
        ),
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Please enter your email';
        }
        if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
          return 'Please enter a valid email';
        }
        return null;
      },
    );
  }
  
  Widget _buildPasswordField() {
    return TextFormField(
      controller: _passwordController,
      obscureText: !_isPasswordVisible,
      style: AppTextStyles.bodyLarge,
      decoration: InputDecoration(
        labelText: 'Password',
        labelStyle: AppTextStyles.bodyMedium.copyWith(
          color: AppColors.textSecondary,
        ),
        prefixIcon: Icon(
          Icons.lock_outlined,
          color: AppColors.textSecondary,
        ),
        suffixIcon: IconButton(
          icon: Icon(
            _isPasswordVisible ? Icons.visibility_off : Icons.visibility,
            color: AppColors.textSecondary,
          ),
          onPressed: () {
            setState(() => _isPasswordVisible = !_isPasswordVisible);
          },
        ),
        filled: true,
        fillColor: AppColors.surfaceVariant,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide.none,
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: AppColors.primary, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: AppColors.error, width: 2),
        ),
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Please enter your password';
        }
        if (value.length < 6) {
          return 'Password must be at least 6 characters';
        }
        return null;
      },
    );
  }
  
  Widget _buildLoginButton() {
    return AppButton(
      text: 'Sign In',
      onPressed: _isLoading ? null : _handleLogin,
      isLoading: _isLoading,
      icon: Icons.login,
    );
  }
  
  Widget _buildAdditionalOptions() {
    return Column(
      children: [
        // Biometric Login
        OutlinedButton.icon(
          onPressed: () {
            // Handle biometric login
          },
          icon: const Icon(Icons.fingerprint),
          label: const Text('Sign in with Biometrics'),
          style: OutlinedButton.styleFrom(
            foregroundColor: AppColors.primary,
            side: BorderSide(color: AppColors.primary),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            padding: const EdgeInsets.symmetric(vertical: 16),
          ),
        ),
        const SizedBox(height: 16),
        
        // Divider
        Row(
          children: [
            Expanded(child: Divider(color: AppColors.divider)),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Text(
                'Don\'t have an account?',
                style: AppTextStyles.bodyMedium.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
            ),
            Expanded(child: Divider(color: AppColors.divider)),
          ],
        ),
        const SizedBox(height: 16),
        
        // Register Button
        TextButton(
          onPressed: () {
            Navigator.pushNamed(context, AppRoutes.register);
          },
          child: Text(
            'Create Account',
            style: AppTextStyles.bodyLarge.copyWith(
              color: AppColors.primary,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ],
    );
  }
  
  Widget _buildSecurityNotice() {
    return AppCard(
      backgroundColor: AppColors.primary.withOpacity(0.1),
      child: Row(
        children: [
          Icon(
            Icons.security,
            color: AppColors.primary,
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              'Your login is protected by advanced behavioral authentication and encryption.',
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.primary,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
